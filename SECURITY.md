# Security Best Practices

This document outlines the security best practices for handling sensitive information in the Wiz Aroma Delivery Bot application.

## Environment Variables

All sensitive information should be stored in environment variables through a `.env` file. This file should **never** be committed to the repository.

### Required Environment Variables

- `BOT_TOKEN`: The Telegram bot token for the user bot
- `ADMIN_BOT_TOKEN`: The Telegram bot token for the admin bot
- `FINANCE_BOT_TOKEN`: The Telegram bot token for the finance bot
- `MAIN<PERSON>NANCE_BOT_TOKEN`: The Telegram bot token for the maintenance bot
- `ADMIN_CHAT_IDS`: JSON array of admin user IDs
- `FINANCE_CHAT_ID`: Finance chat ID
- `MAINTENANCE_CHAT_ID`: Maintenance chat ID
- `EMAIL_ADDRESS`: Email address for sending notifications
- `EMAIL_PASSWORD`: Email password or app-specific password
- `FIREBASE_DATABASE_URL`: Firebase database URL
- `FIREBASE_CREDENTIALS`: Firebase service account credentials (as <PERSON><PERSON><PERSON> string)

## Firebase Credentials

There are two ways to provide Firebase credentials to the application:

### Option 1: Environment Variable (Recommended for Production)

Store the entire Firebase credentials JSON in an environment variable:

```
FIREBASE_CREDENTIALS={"type":"service_account","project_id":"your-project-id","private_key_id":"key-id","private_key":"-----BEGIN PRIVATE KEY-----\nkey-content\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk%40your-project-id.iam.gserviceaccount.com"}
```

This is the preferred method for production environments like Railway or Heroku.

### Option 2: JSON File (Development)

For local development, you can download the Firebase credentials JSON file and save it as `firebase-credentials.json` in the project root directory.

**IMPORTANT: Never commit this file to the repository.** It should be added to `.gitignore`.

You can also specify a custom path for this file using the `FIREBASE_CREDENTIALS_PATH` environment variable.

## Deployment Secrets

When deploying to platforms like Railway, Heroku, or GitHub Actions, use the platform's secrets management feature to store sensitive information:

- For Railway: Add environment variables in the Railway dashboard
- For Heroku: Use config vars in the Heroku dashboard
- For GitHub Actions: Use repository secrets in the GitHub repository settings

## Google Cloud Keys Security

Google Cloud service account keys (like Firebase credentials) are particularly sensitive as they provide access to your Google Cloud resources. Follow these best practices:

1. Use the principle of least privilege when creating service accounts
2. Rotate service account keys regularly
3. Monitor service account activity
4. Use environment variables instead of files when possible
5. Never commit service account keys to your repository
6. Consider using GitHub's secret scanning to prevent accidental leaks

## Secret Rotation

Regularly rotate all secrets and credentials, including:

- Telegram bot tokens
- Email passwords
- Firebase service account keys
- Database credentials

## Code Reviews

When reviewing code changes, pay special attention to:

- Hardcoded credentials or tokens
- Changes to the Firebase initialization code
- Changes to the .gitignore file that might expose sensitive files
- Log statements that might expose sensitive information

## Secure Storage

- Local development: Use .env files not tracked by git
- CI/CD: Use the platform's secret management features
- Production: Use environment variables or dedicated secret management services 