#!/usr/bin/env python3
"""
Verify the delivery system fixes
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_authorization_fix():
    """Verify authorization fix"""
    print("🔐 VERIFYING AUTHORIZATION FIX")
    print("=" * 50)
    
    try:
        from src.config import DELIVERY_BOT_AUTHORIZED_IDS
        
        print(f"Current authorized IDs: {DELIVERY_BOT_AUTHORIZED_IDS}")
        
        if 1133538088 in DELIVERY_BOT_AUTHORIZED_IDS:
            print("✅ Telegram ID 1133538088 is authorized for delivery bot")
            return True
        else:
            print("❌ Telegram ID 1133538088 NOT authorized")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def verify_customer_confirmation():
    """Verify customer confirmation workflow"""
    print("\n👤 VERIFYING CUSTOMER CONFIRMATION WORKFLOW")
    print("=" * 50)
    
    try:
        # Test imports
        from src.bots.order_track_bot import send_customer_confirmation_request
        from src.bot_instance import bot
        from src.handlers.order_handlers import handle_delivery_confirmation
        
        print("✅ All customer confirmation imports successful")
        
        # Test function signature
        import inspect
        sig = inspect.signature(send_customer_confirmation_request)
        params = list(sig.parameters.keys())
        
        if 'order_number' in params:
            print("✅ send_customer_confirmation_request has correct signature")
            return True
        else:
            print(f"❌ Unexpected signature: {params}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main verification function"""
    print("🔍 VERIFYING DELIVERY SYSTEM FIXES")
    print("=" * 80)
    
    auth_ok = verify_authorization_fix()
    confirmation_ok = verify_customer_confirmation()
    
    print("\n" + "=" * 80)
    print("📊 VERIFICATION RESULTS:")
    print(f"   Authorization Fix: {'✅ Success' if auth_ok else '❌ Failed'}")
    print(f"   Customer Confirmation: {'✅ Success' if confirmation_ok else '❌ Failed'}")
    
    if auth_ok and confirmation_ok:
        print("\n🎉 ALL FIXES VERIFIED!")
        print("\n📋 NEXT STEPS:")
        print("1. Add delivery personnel with Telegram ID 1133538088 to Firebase")
        print("2. Test complete order workflow with all three personnel")
        print("3. Verify customer confirmation buttons appear correctly")
        return True
    else:
        print("\n⚠️  Some verifications failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
